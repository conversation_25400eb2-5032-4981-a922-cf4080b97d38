import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Eye, Pencil, Trash2 } from "lucide-react";
import CreateStoreModal from "@/components/stores/CreateStoreModal";
import React, { useState } from "react";
import { useToast } from "@/hooks/useToast";
import { ToastContainer } from "@/components/common/Toast";

interface Holiday {
  holiday: string;
  day: string;
  date: string;
}

const HolidayListPage = () => {
  const [selectedYear, setSelectedYear] = useState("2025");

  const mockHolidays: Holiday[] = [
    { holiday: "New Year", day: "Wednesday", date: "01-01-2025" },
    { holiday: "Good Friday", day: "Friday", date: "04-18-2025" },
    { holiday: "Easter Monday", day: "Monday", date: "04-21-2025" },
    { holiday: "Victoria Day", day: "Monday", date: "05-19-2025" },
    { holiday: "Saint-Jean-Baptiste Day", day: "Tuesday", date: "06-24-2025" },
    { holiday: "Canada Day", day: "Tuesday", date: "07-01-2025" },
    { holiday: "Civic Holiday", day: "Monday", date: "08-04-2025" },
    { holiday: "Labour Day", day: "Monday", date: "09-01-2025" },
    {
      holiday: "National Day for Truth & Reconciliation",
      day: "Tuesday",
      date: "09-30-2025",
    },
    { holiday: "Thanksgiving Day", day: "Monday", date: "10-13-2025" },
    { holiday: "Remembrance Day", day: "Tuesday", date: "11-11-2025" },
    { holiday: "Christmas Day", day: "Thursday", date: "12-25-2025" },
    { holiday: "Boxing Day", day: "Friday", date: "12-26-2025" },
  ];

  return (
    <div>
      <Card className="rounded-xl shadow-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Holiday List</CardTitle>
            <Select value={selectedYear} onValueChange={setSelectedYear}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select year" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2023">2023</SelectItem>
                <SelectItem value="2024">2024</SelectItem>
                <SelectItem value="2025">2025</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border bg-background">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="font-semibold">Holiday Name</TableHead>
                  <TableHead className="font-semibold">Day</TableHead>
                  <TableHead className="font-semibold">Date</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockHolidays.map((holiday, index) => (
                  <TableRow key={index}>
                    <TableCell className="py-3 px-2">{holiday.holiday}</TableCell>
                    <TableCell className="py-3">{holiday.day}</TableCell>
                    <TableCell className="py-3">{holiday.date}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default HolidayListPage;
