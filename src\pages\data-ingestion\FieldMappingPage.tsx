import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  FileText,
  Database,
  Check,
  X,
  Eye,
  Save,
  ArrowRight,
  Table,
  Columns,
  AlertCircle,
  CheckCircle,
  Minus,
  Sparkles,
  Loader2,
} from "lucide-react";
import { useToast } from "@/hooks/useToast";
import { ToastContainer } from "@/components/common/Toast";
import ProcessStepper from "@/components/data-ingestion/ProcessStepper";

interface TableSchema {
  id: string;
  name: string;
  description: string;
  fields: SchemaField[];
}

interface SchemaField {
  id: string;
  name: string;
  type: string;
  description: string;
  isRequired: boolean;
  sampleData?: string[];
}

interface FieldMapping {
  sourceFieldId: string;
  targetFieldId: string;
  tableId: string;
}

interface TableStatus {
  tableId: string;
  requiredFields: number;
  mappedFields: number;
  isComplete: boolean;
}

export default function FieldMappingPage() {
  const { batchId } = useParams();
  const navigate = useNavigate();
  const { toasts, removeToast, success, error, warning } = useToast();

  const [flashanaTables] = useState<TableSchema[]>([
    {
      id: "sales",
      name: "Sales Transactions",
      description: "Primary sales transaction data",
      fields: [
        {
          id: "sales_sku",
          name: "sku",
          type: "string",
          description: "Product SKU identifier",
          isRequired: true,
        },
        {
          id: "sales_title",
          name: "product_title",
          type: "string",
          description: "Product display name",
          isRequired: true,
        },
        {
          id: "sales_price",
          name: "unit_price",
          type: "decimal",
          description: "Price per unit in CAD",
          isRequired: true,
        },
        {
          id: "sales_qty",
          name: "units_sold",
          type: "integer",
          description: "Number of units sold",
          isRequired: true,
        },
        {
          id: "sales_date",
          name: "transaction_date",
          type: "datetime",
          description: "Date and time of transaction",
          isRequired: true,
        },
        {
          id: "sales_customer",
          name: "customer_id",
          type: "string",
          description: "Customer identifier",
          isRequired: false,
        },
        {
          id: "sales_discount",
          name: "discount_amount",
          type: "decimal",
          description: "Discount amount in CAD",
          isRequired: false,
        },
        {
          id: "sales_store",
          name: "store_code",
          type: "string",
          description: "Store location code",
          isRequired: false,
        },
      ],
    },
    {
      id: "products",
      name: "Product Catalog",
      description: "Product information and details",
      fields: [
        {
          id: "prod_sku",
          name: "sku",
          type: "string",
          description: "Product SKU identifier",
          isRequired: true,
        },
        {
          id: "prod_name",
          name: "product_name",
          type: "string",
          description: "Product name",
          isRequired: true,
        },
        {
          id: "prod_category",
          name: "category",
          type: "string",
          description: "Product category",
          isRequired: true,
        },
        {
          id: "prod_brand",
          name: "brand",
          type: "string",
          description: "Product brand",
          isRequired: false,
        },
        {
          id: "prod_cost",
          name: "cost_price",
          type: "decimal",
          description: "Product cost price",
          isRequired: false,
        },
        {
          id: "prod_retail",
          name: "retail_price",
          type: "decimal",
          description: "Retail price",
          isRequired: true,
        },
      ],
    },
    {
      id: "customers",
      name: "Customer Information",
      description: "Customer demographics and contact details",
      fields: [
        {
          id: "cust_id",
          name: "customer_id",
          type: "string",
          description: "Unique customer identifier",
          isRequired: true,
        },
        {
          id: "cust_email",
          name: "email",
          type: "string",
          description: "Customer email address",
          isRequired: true,
        },
        {
          id: "cust_fname",
          name: "first_name",
          type: "string",
          description: "Customer first name",
          isRequired: false,
        },
        {
          id: "cust_lname",
          name: "last_name",
          type: "string",
          description: "Customer last name",
          isRequired: false,
        },
        {
          id: "cust_phone",
          name: "phone",
          type: "string",
          description: "Customer phone number",
          isRequired: false,
        },
        {
          id: "cust_addr",
          name: "address",
          type: "string",
          description: "Customer address",
          isRequired: false,
        },
      ],
    },
  ]);

  const [uploadedFiles] = useState([
    {
      id: "file1",
      name: "sales_data_q4.csv",
      fields: [
        {
          id: "src1",
          name: "product_id",
          type: "string",
          sampleData: ["PRD001", "PRD002", "PRD003"],
        },
        {
          id: "src2",
          name: "product_name",
          type: "string",
          sampleData: ["Widget A", "Widget B", "Widget C"],
        },
        {
          id: "src3",
          name: "price",
          type: "number",
          sampleData: ["19.99", "29.99", "39.99"],
        },
        {
          id: "src4",
          name: "quantity_sold",
          type: "number",
          sampleData: ["10", "25", "15"],
        },
        {
          id: "src5",
          name: "sale_date",
          type: "date",
          sampleData: ["2024-01-15", "2024-01-16", "2024-01-17"],
        },
        {
          id: "src6",
          name: "customer_email",
          type: "string",
          sampleData: ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
        },
        {
          id: "src7",
          name: "discount_percent",
          type: "number",
          sampleData: ["0", "10", "5"],
        },
        {
          id: "src8",
          name: "store_location",
          type: "string",
          sampleData: ["Toronto", "Vancouver", "Calgary"],
        },
      ],
    },
    {
      id: "file2",
      name: "product_catalog.xlsx",
      fields: [
        {
          id: "prod1",
          name: "sku",
          type: "string",
          sampleData: ["PRD001", "PRD002", "PRD003"],
        },
        {
          id: "prod2",
          name: "name",
          type: "string",
          sampleData: ["Widget A", "Widget B", "Widget C"],
        },
        {
          id: "prod3",
          name: "category",
          type: "string",
          sampleData: ["Electronics", "Home", "Garden"],
        },
        {
          id: "prod4",
          name: "brand",
          type: "string",
          sampleData: ["BrandA", "BrandB", "BrandC"],
        },
        {
          id: "prod5",
          name: "cost",
          type: "number",
          sampleData: ["15.99", "22.99", "31.99"],
        },
        {
          id: "prod6",
          name: "retail",
          type: "number",
          sampleData: ["19.99", "29.99", "39.99"],
        },
      ],
    },
  ]);

  const [selectedFlashanaTable, setSelectedFlashanaTable] =
    useState<string>("");
  const [selectedUploadedFile, setSelectedUploadedFile] = useState<string>("");
  const [mappings, setMappings] = useState<FieldMapping[]>([]);
  const [draggedField, setDraggedField] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [isAutoMapping, setIsAutoMapping] = useState(false);

  const getCurrentFlashanaTable = () =>
    flashanaTables.find((t) => t.id === selectedFlashanaTable);
  const getCurrentUploadedFile = () =>
    uploadedFiles.find((f) => f.id === selectedUploadedFile);

  const getTableStatuses = (): TableStatus[] => {
    return flashanaTables.map((table) => {
      const requiredFields = table.fields.filter((f) => f.isRequired).length;
      const mappedFields = mappings.filter(
        (m) =>
          m.tableId === table.id &&
          table.fields.find((f) => f.id === m.targetFieldId)?.isRequired
      ).length;

      return {
        tableId: table.id,
        requiredFields,
        mappedFields,
        isComplete: mappedFields === requiredFields,
      };
    });
  };

  const getOverallStatus = () => {
    const statuses = getTableStatuses();
    const completeTables = statuses.filter((s) => s.isComplete).length;
    const totalRequiredMappings = statuses.reduce(
      (sum, s) => sum + s.requiredFields,
      0
    );
    const totalMappedRequired = statuses.reduce(
      (sum, s) => sum + s.mappedFields,
      0
    );

    return {
      completeTables,
      totalTables: flashanaTables.length,
      totalRequiredMappings,
      totalMappedRequired,
      isAllComplete: completeTables === flashanaTables.length,
      statusText:
        completeTables === 0
          ? "Not Mapped"
          : completeTables === flashanaTables.length
            ? "Mapped"
            : "Partially Mapped",
    };
  };

  const handleDragStart = (fieldId: string) => {
    setDraggedField(fieldId);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, targetFieldId: string) => {
    e.preventDefault();
    if (draggedField && selectedFlashanaTable) {
      // Remove existing mapping for this target field
      const newMappings = mappings.filter(
        (m) => m.targetFieldId !== targetFieldId
      );
      // Add new mapping
      newMappings.push({
        sourceFieldId: draggedField,
        targetFieldId,
        tableId: selectedFlashanaTable,
      });
      setMappings(newMappings);
      setDraggedField(null);
    }
  };

  const removeMapping = (targetFieldId: string) => {
    setMappings(mappings.filter((m) => m.targetFieldId !== targetFieldId));
  };

  const getMappingForTarget = (targetFieldId: string) =>
    mappings.find(
      (m) =>
        m.targetFieldId === targetFieldId && m.tableId === selectedFlashanaTable
    );

  const getSourceFieldById = (fieldId: string) => {
    const currentFile = getCurrentUploadedFile();
    return currentFile?.fields.find((f) => f.id === fieldId);
  };

  const handleSaveMapping = () => {
    console.log("Saving mappings:", mappings);
    success(
      "Mappings saved successfully!",
      "Field mappings have been saved to the system."
    );
  };

  const handlePreview = () => {
    if (!selectedFlashanaTable || !selectedUploadedFile) {
      error(
        "Missing Selection",
        "Please select both Flashana table and uploaded file first."
      );
      return;
    }
    setShowPreview(true);
  };

  const handleAutoMap = async () => {
    if (!selectedFlashanaTable || !selectedUploadedFile) {
      error(
        "Missing Selection",
        "Please select both Flashana table and uploaded file first."
      );
      return;
    }

    setIsAutoMapping(true);

    // Simulate AI processing time
    await new Promise((resolve) => setTimeout(resolve, 2000));

    const currentTable = getCurrentFlashanaTable();
    const currentFile = getCurrentUploadedFile();

    if (!currentTable || !currentFile) {
      setIsAutoMapping(false);
      return;
    }

    // AI-based mapping logic
    const newMappings: FieldMapping[] = [];

    // Create a mapping between common field name patterns
    const mappingRules: Record<string, string[]> = {
      sku: ["sku", "product_id", "item_id", "product_code"],
      product_title: [
        "product_name",
        "product_title",
        "name",
        "title",
        "item_name",
      ],
      unit_price: ["price", "unit_price", "cost", "amount", "retail_price"],
      units_sold: [
        "quantity",
        "qty",
        "units_sold",
        "quantity_sold",
        "amount_sold",
      ],
      transaction_date: [
        "date",
        "sale_date",
        "transaction_date",
        "purchase_date",
        "created_at",
      ],
      customer_id: [
        "customer_id",
        "customer_email",
        "customer",
        "buyer_id",
        "user_id",
      ],
      discount_amount: [
        "discount",
        "discount_amount",
        "discount_percent",
        "reduction",
      ],
      store_code: [
        "store",
        "store_code",
        "store_location",
        "location",
        "branch",
      ],
      product_name: ["product_name", "name", "title", "item_name"],
      category: ["category", "product_category", "type", "classification"],
      brand: ["brand", "manufacturer", "company", "brand_name"],
      cost_price: ["cost", "cost_price", "wholesale_price", "purchase_price"],
      retail_price: ["retail", "retail_price", "selling_price", "price"],
      email: ["email", "customer_email", "contact_email", "user_email"],
      first_name: ["first_name", "fname", "given_name", "firstname"],
      last_name: ["last_name", "lname", "surname", "lastname"],
      phone: ["phone", "telephone", "mobile", "contact_number"],
      address: ["address", "location", "street_address", "addr"],
    };

    // Auto-map fields based on similarity
    currentTable.fields.forEach((targetField) => {
      const targetFieldName = targetField.name.toLowerCase();
      const possibleMatches = mappingRules[targetFieldName] || [
        targetFieldName,
      ];

      // Find best match from source fields
      const sourceField = currentFile.fields.find((field) => {
        const sourceFieldName = field.name.toLowerCase();
        return possibleMatches.some(
          (pattern) =>
            sourceFieldName.includes(pattern) ||
            pattern.includes(sourceFieldName)
        );
      });

      if (
        sourceField &&
        !newMappings.some((m) => m.sourceFieldId === sourceField.id)
      ) {
        newMappings.push({
          sourceFieldId: sourceField.id,
          targetFieldId: targetField.id,
          tableId: selectedFlashanaTable,
        });
      }
    });

    // Update mappings for current table
    const otherMappings = mappings.filter(
      (m) => m.tableId !== selectedFlashanaTable
    );
    setMappings([...otherMappings, ...newMappings]);

    setIsAutoMapping(false);

    // Show success message
    const mappedCount = newMappings.length;
    const requiredCount = currentTable.fields.filter(
      (f) => f.isRequired
    ).length;

    if (mappedCount >= requiredCount) {
      success(
        "AI Mapping Complete!",
        `Successfully mapped ${mappedCount} fields. All required fields have been mapped.`
      );
    } else {
      warning(
        "AI Mapping Partial",
        `Mapped ${mappedCount} fields. Please manually map the remaining ${requiredCount - mappedCount} required fields.`
      );
    }
  };

  const handleContinueToValidation = () => {
    const overallStatus = getOverallStatus();
    if (overallStatus.isAllComplete) {
      navigate(`/data-ingestion/validation/${batchId}`);
    } else {
      error(
        "Mapping Incomplete",
        "Please map all required fields in all tables before continuing to validation."
      );
    }
  };

  const overallStatus = getOverallStatus();
  const tableStatuses = getTableStatuses();
  const currentTable = getCurrentFlashanaTable();
  const currentFile = getCurrentUploadedFile();

  return (
    <div style={{ padding: "1.5rem" }}>
      {/* Process Stepper */}
      <ProcessStepper currentStep={1} />

      {/* Header */}
      <div
        style={{
          display: "flex",
          alignItems: "center",
          gap: "1rem",
          marginBottom: "2rem",
        }}
      >
        <button
          onClick={() => navigate("/data-ingestion/batches")}
          style={{
            padding: "0.5rem",
            color: "#71717a",
            backgroundColor: "transparent",
            border: "1px solid #e4e4e7",
            borderRadius: "0.375rem",
            cursor: "pointer",
            transition: "all 0.2s",
          }}
        >
          <ArrowLeft size={16} />
        </button>
        <div>
          <h1
            style={{
              fontSize: "1.875rem",
              fontWeight: "600",
              color: "#18181b",
              marginBottom: "0.25rem",
            }}
          >
            Field Mapping
          </h1>
          <p style={{ color: "#3f3f46" }}>
            Batch ID: {batchId} • Map uploaded file fields to Flashana table
            schema
          </p>
        </div>
      </div>

      {/* Main Layout */}
      <div
        style={{ display: "grid", gridTemplateColumns: "2fr 1fr", gap: "2rem" }}
      >
        {/* Schema Mapping Section */}
        <div>
          <div
            style={{
              backgroundColor: "white",
              borderRadius: "0.5rem",
              border: "1px solid #e4e4e7",
              padding: "1.5rem",
              marginBottom: "1.5rem",
            }}
          >
            <h2
              style={{
                fontSize: "1.25rem",
                fontWeight: "600",
                color: "#18181b",
                marginBottom: "1.5rem",
              }}
            >
              Schema Mapping
            </h2>

            {/* Dropdown Selections */}
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gap: "1rem",
                marginBottom: "1.5rem",
              }}
            >
              <div>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    color: "#3f3f46",
                    marginBottom: "0.5rem",
                  }}
                >
                  Select Flashana Table Schema
                </label>
                <select
                  value={selectedFlashanaTable}
                  onChange={(e) => setSelectedFlashanaTable(e.target.value)}
                  style={{
                    width: "100%",
                    padding: "0.75rem",
                    border: "1px solid #e4e4e7",
                    borderRadius: "0.375rem",
                    backgroundColor: "white",
                    color: "#18181b",
                    fontSize: "0.875rem",
                    outline: "none",
                  }}
                >
                  <option value="">Select Table Schema...</option>
                  {flashanaTables.map((table) => (
                    <option key={table.id} value={table.id}>
                      {table.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    color: "#3f3f46",
                    marginBottom: "0.5rem",
                  }}
                >
                  Select Uploaded File Schema
                </label>
                <select
                  value={selectedUploadedFile}
                  onChange={(e) => setSelectedUploadedFile(e.target.value)}
                  style={{
                    width: "100%",
                    padding: "0.75rem",
                    border: "1px solid #e4e4e7",
                    borderRadius: "0.375rem",
                    backgroundColor: "white",
                    color: "#18181b",
                    fontSize: "0.875rem",
                    outline: "none",
                  }}
                >
                  <option value="">Select File Schema...</option>
                  {uploadedFiles.map((file) => (
                    <option key={file.id} value={file.id}>
                      {file.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Auto-map Button */}
            {selectedFlashanaTable && selectedUploadedFile && (
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  marginBottom: "2rem",
                }}
              >
                <button
                  onClick={handleAutoMap}
                  disabled={isAutoMapping}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                    backgroundColor: isAutoMapping ? "#9ca3af" : "#7c3aed",
                    color: "white",
                    borderRadius: "0.375rem",
                    padding: "0.75rem 1.5rem",
                    fontWeight: "500",
                    fontSize: "0.875rem",
                    border: "none",
                    cursor: isAutoMapping ? "not-allowed" : "pointer",
                    transition: "all 0.2s",
                    boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                  }}
                >
                  {isAutoMapping ? (
                    <>
                      <Loader2
                        size={16}
                        style={{ animation: "spin 1s linear infinite" }}
                      />
                      Mapping with AI...
                    </>
                  ) : (
                    <>
                      <Sparkles size={16} />
                      Auto-map with AI
                    </>
                  )}
                </button>
              </div>
            )}

            {/* Field Mapping Interface */}
            {selectedFlashanaTable && selectedUploadedFile && (
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: "2rem",
                }}
              >
                {/* Flashana Table Fields (Left - Drop Targets) */}
                <div>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "0.5rem",
                      marginBottom: "1rem",
                    }}
                  >
                    <Database size={18} style={{ color: "#2b524f" }} />
                    <h3
                      style={{
                        fontSize: "1rem",
                        fontWeight: "600",
                        color: "#18181b",
                        margin: 0,
                      }}
                    >
                      {currentTable?.name} Fields
                    </h3>
                    <div
                      style={{
                        backgroundColor: "#f0fdf4",
                        color: "#166534",
                        padding: "0.125rem 0.5rem",
                        borderRadius: "0.375rem",
                        fontSize: "0.75rem",
                        fontWeight: "500",
                      }}
                    >
                      {currentTable?.fields.length} fields
                    </div>
                  </div>

                  <div>
                    {currentTable?.fields.map((field) => {
                      const mapping = getMappingForTarget(field.id);
                      const sourceField = mapping
                        ? getSourceFieldById(mapping.sourceFieldId)
                        : null;

                      return (
                        <div
                          key={field.id}
                          onDragOver={handleDragOver}
                          onDrop={(e) => handleDrop(e, field.id)}
                          style={{
                            padding: "1rem",
                            border: `2px ${mapping ? "solid #2b524f" : "dashed #d1d5db"}`,
                            borderRadius: "0.375rem",
                            backgroundColor: mapping ? "#f0fdf4" : "#fafafa",
                            transition: "all 0.2s",
                            marginBottom: "0.75rem",
                            minHeight: "80px",
                          }}
                        >
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                              marginBottom: "0.5rem",
                            }}
                          >
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                gap: "0.5rem",
                              }}
                            >
                              <span
                                style={{ fontWeight: "500", color: "#18181b" }}
                              >
                                {field.name}
                              </span>
                              {field.isRequired && (
                                <span
                                  style={{
                                    color: "#dc2626",
                                    fontSize: "0.75rem",
                                    backgroundColor: "#fef2f2",
                                    padding: "0.125rem 0.375rem",
                                    borderRadius: "0.25rem",
                                    border: "1px solid #fecaca",
                                  }}
                                >
                                  Required
                                </span>
                              )}
                              {mapping && (
                                <CheckCircle
                                  size={16}
                                  style={{ color: "#16a34a" }}
                                />
                              )}
                            </div>
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                gap: "0.5rem",
                              }}
                            >
                              <span
                                style={{
                                  fontSize: "0.75rem",
                                  color: "#71717a",
                                  backgroundColor: "#f8fafc",
                                  padding: "0.125rem 0.375rem",
                                  borderRadius: "0.25rem",
                                }}
                              >
                                {field.type}
                              </span>
                              {mapping && (
                                <button
                                  onClick={() => removeMapping(field.id)}
                                  style={{
                                    padding: "0.25rem",
                                    color: "#dc2626",
                                    backgroundColor: "transparent",
                                    border: "none",
                                    borderRadius: "0.25rem",
                                    cursor: "pointer",
                                  }}
                                >
                                  <X size={12} />
                                </button>
                              )}
                            </div>
                          </div>

                          <div
                            style={{
                              fontSize: "0.75rem",
                              color: "#71717a",
                              marginBottom: "0.5rem",
                            }}
                          >
                            {field.description}
                          </div>

                          {mapping && sourceField ? (
                            <div
                              style={{
                                backgroundColor: "white",
                                padding: "0.5rem",
                                borderRadius: "0.25rem",
                                border: "1px solid #e5e7eb",
                                fontSize: "0.75rem",
                              }}
                            >
                              <div
                                style={{
                                  fontWeight: "500",
                                  color: "#16a34a",
                                  marginBottom: "0.25rem",
                                }}
                              >
                                ← Mapped from: {sourceField.name}
                              </div>
                              <div style={{ color: "#71717a" }}>
                                Sample:{" "}
                                {sourceField.sampleData?.slice(0, 2).join(", ")}
                              </div>
                            </div>
                          ) : (
                            <div
                              style={{
                                fontSize: "0.75rem",
                                color: "#71717a",
                                fontStyle: "italic",
                                textAlign: "center",
                                padding: "0.5rem",
                              }}
                            >
                              Drop file field here to map
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Uploaded File Fields (Right - Drag Sources) */}
                <div>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "0.5rem",
                      marginBottom: "1rem",
                    }}
                  >
                    <FileText size={18} style={{ color: "#ab732b" }} />
                    <h3
                      style={{
                        fontSize: "1rem",
                        fontWeight: "600",
                        color: "#18181b",
                        margin: 0,
                      }}
                    >
                      {currentFile?.name} Fields
                    </h3>
                    <div
                      style={{
                        backgroundColor: "#fef3c7",
                        color: "#d97706",
                        padding: "0.125rem 0.5rem",
                        borderRadius: "0.375rem",
                        fontSize: "0.75rem",
                        fontWeight: "500",
                      }}
                    >
                      {currentFile?.fields.length} fields
                    </div>
                  </div>

                  <div>
                    {currentFile?.fields.map((field) => (
                      <div
                        key={field.id}
                        draggable
                        onDragStart={() => handleDragStart(field.id)}
                        style={{
                          padding: "1rem",
                          border: "1px solid #e4e4e7",
                          borderRadius: "0.375rem",
                          backgroundColor: "white",
                          cursor: "move",
                          transition: "all 0.2s",
                          marginBottom: "0.75rem",
                        }}
                        onMouseOver={(e) => {
                          e.currentTarget.style.boxShadow =
                            "0 2px 4px rgba(0,0,0,0.1)";
                          e.currentTarget.style.borderColor = "#ab732b";
                        }}
                        onMouseOut={(e) => {
                          e.currentTarget.style.boxShadow = "none";
                          e.currentTarget.style.borderColor = "#e4e4e7";
                        }}
                      >
                        <div
                          style={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            marginBottom: "0.5rem",
                          }}
                        >
                          <span style={{ fontWeight: "500", color: "#18181b" }}>
                            {field.name}
                          </span>
                          <span
                            style={{
                              fontSize: "0.75rem",
                              color: "#71717a",
                              backgroundColor: "#f8fafc",
                              padding: "0.125rem 0.375rem",
                              borderRadius: "0.25rem",
                            }}
                          >
                            {field.type}
                          </span>
                        </div>

                        <div style={{ fontSize: "0.75rem", color: "#71717a" }}>
                          <strong>Sample:</strong>{" "}
                          {field.sampleData?.slice(0, 3).join(", ")}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            {selectedFlashanaTable && selectedUploadedFile && (
              <div
                style={{
                  display: "flex",
                  gap: "1rem",
                  marginTop: "2rem",
                  paddingTop: "1rem",
                  borderTop: "1px solid #e4e4e7",
                }}
              >
                <button
                  onClick={handleSaveMapping}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                    backgroundColor: "#2b524f",
                    color: "white",
                    borderRadius: "0.375rem",
                    padding: "0.625rem 1rem",
                    fontWeight: "500",
                    fontSize: "0.875rem",
                    border: "none",
                    cursor: "pointer",
                  }}
                >
                  <Save size={14} />
                  Save Mapping
                </button>

                <button
                  onClick={handlePreview}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                    backgroundColor: "#ab732b",
                    color: "white",
                    borderRadius: "0.375rem",
                    padding: "0.625rem 1rem",
                    fontWeight: "500",
                    fontSize: "0.875rem",
                    border: "none",
                    cursor: "pointer",
                  }}
                >
                  <Eye size={14} />
                  Preview
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Status Section */}
        <div>
          <div
            style={{
              backgroundColor: "white",
              borderRadius: "0.5rem",
              border: "1px solid #e4e4e7",
              padding: "1.5rem",
            }}
          >
            <h2
              style={{
                fontSize: "1.25rem",
                fontWeight: "600",
                color: "#18181b",
                marginBottom: "1rem",
              }}
            >
              Status
            </h2>

            {/* Overall Status */}
            <div
              style={{
                backgroundColor: "#f8fafc",
                border: "1px solid #e2e8f0",
                borderRadius: "0.375rem",
                padding: "1rem",
                marginBottom: "1.5rem",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "0.5rem",
                  marginBottom: "0.5rem",
                }}
              >
                {overallStatus.isAllComplete ? (
                  <CheckCircle size={20} style={{ color: "#16a34a" }} />
                ) : overallStatus.totalMappedRequired > 0 ? (
                  <AlertCircle size={20} style={{ color: "#d97706" }} />
                ) : (
                  <Minus size={20} style={{ color: "#71717a" }} />
                )}
                <span
                  style={{
                    fontWeight: "600",
                    fontSize: "1rem",
                    color: "#18181b",
                  }}
                >
                  {overallStatus.statusText}
                </span>
              </div>
              <div style={{ fontSize: "0.875rem", color: "#71717a" }}>
                {overallStatus.completeTables} of {overallStatus.totalTables}{" "}
                tables completed
              </div>
              <div style={{ fontSize: "0.875rem", color: "#71717a" }}>
                {overallStatus.totalMappedRequired} of{" "}
                {overallStatus.totalRequiredMappings} required fields mapped
              </div>
            </div>

            {/* Table Status List */}
            <div>
              <h3
                style={{
                  fontSize: "1rem",
                  fontWeight: "600",
                  color: "#18181b",
                  marginBottom: "1rem",
                }}
              >
                Required Tables
              </h3>

              <div>
                {flashanaTables.map((table) => {
                  const status = tableStatuses.find(
                    (s) => s.tableId === table.id
                  );
                  if (!status) return null;

                  return (
                    <div
                      key={table.id}
                      style={{
                        border: "1px solid #e4e4e7",
                        borderRadius: "0.375rem",
                        padding: "1rem",
                        marginBottom: "0.75rem",
                        backgroundColor: status.isComplete
                          ? "#f0fdf4"
                          : "#fafafa",
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                          marginBottom: "0.75rem",
                        }}
                      >
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "0.5rem",
                          }}
                        >
                          {status.isComplete ? (
                            <CheckCircle
                              size={16}
                              style={{ color: "#16a34a" }}
                            />
                          ) : status.mappedFields > 0 ? (
                            <AlertCircle
                              size={16}
                              style={{ color: "#d97706" }}
                            />
                          ) : (
                            <Minus size={16} style={{ color: "#71717a" }} />
                          )}
                          <span style={{ fontWeight: "500", color: "#18181b" }}>
                            {table.name}
                          </span>
                        </div>
                        <span
                          style={{
                            fontSize: "0.75rem",
                            color: "#71717a",
                            backgroundColor: "#f8fafc",
                            padding: "0.125rem 0.375rem",
                            borderRadius: "0.25rem",
                          }}
                        >
                          {status.mappedFields}/{status.requiredFields}
                        </span>
                      </div>

                      <div
                        style={{
                          fontSize: "0.75rem",
                          color: "#71717a",
                          marginBottom: "0.75rem",
                        }}
                      >
                        {table.description}
                      </div>

                      {/* Field Status */}
                      <div>
                        {table.fields
                          .filter((f) => f.isRequired)
                          .map((field) => {
                            const mapping = mappings.find(
                              (m) =>
                                m.targetFieldId === field.id &&
                                m.tableId === table.id
                            );
                            return (
                              <div
                                key={field.id}
                                style={{
                                  display: "flex",
                                  justifyContent: "space-between",
                                  alignItems: "center",
                                  fontSize: "0.75rem",
                                  padding: "0.25rem 0",
                                  borderBottom: "1px solid #f1f5f9",
                                }}
                              >
                                <div
                                  style={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: "0.25rem",
                                  }}
                                >
                                  {mapping ? (
                                    <Check
                                      size={12}
                                      style={{ color: "#16a34a" }}
                                    />
                                  ) : (
                                    <X size={12} style={{ color: "#dc2626" }} />
                                  )}
                                  <span style={{ color: "#3f3f46" }}>
                                    {field.name}
                                  </span>
                                </div>
                                <span style={{ color: "#71717a" }}>
                                  {field.type}
                                </span>
                              </div>
                            );
                          })}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Continue Button */}
            <button
              onClick={handleContinueToValidation}
              disabled={!overallStatus.isAllComplete}
              style={{
                display: "flex",
                alignItems: "center",
                gap: "0.5rem",
                backgroundColor: overallStatus.isAllComplete
                  ? "#2b524f"
                  : "#d1d5db",
                color: "white",
                borderRadius: "0.375rem",
                padding: "0.75rem 1rem",
                fontWeight: "500",
                fontSize: "0.875rem",
                border: "none",
                cursor: overallStatus.isAllComplete ? "pointer" : "not-allowed",
                width: "100%",
                justifyContent: "center",
                marginTop: "1rem",
              }}
            >
              Continue to Validation
              <ArrowRight size={14} />
            </button>
          </div>
        </div>
      </div>

      {/* Preview Modal */}
      {showPreview && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 1000,
          }}
        >
          <div
            style={{
              backgroundColor: "white",
              borderRadius: "0.5rem",
              padding: "2rem",
              maxWidth: "900px",
              width: "90%",
              maxHeight: "80vh",
              overflow: "auto",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "1.5rem",
              }}
            >
              <h2
                style={{
                  fontSize: "1.5rem",
                  fontWeight: "600",
                  color: "#18181b",
                  margin: 0,
                }}
              >
                Preview: {currentTable?.name}
              </h2>
              <button
                onClick={() => setShowPreview(false)}
                style={{
                  padding: "0.5rem",
                  color: "#71717a",
                  backgroundColor: "transparent",
                  border: "none",
                  borderRadius: "0.375rem",
                  cursor: "pointer",
                }}
              >
                <X size={20} />
              </button>
            </div>

            <div
              style={{
                backgroundColor: "#f8fafc",
                border: "1px solid #e2e8f0",
                borderRadius: "0.375rem",
                padding: "1rem",
                marginBottom: "1rem",
              }}
            >
              <div
                style={{
                  fontWeight: "600",
                  marginBottom: "0.5rem",
                  color: "#18181b",
                }}
              >
                Sample Mapped Data (Top 5 rows):
              </div>
              <div style={{ fontSize: "0.75rem", color: "#71717a" }}>
                Preview shows how your mapped fields will appear in the Flashana{" "}
                {currentTable?.name} table
              </div>
            </div>

            <div style={{ overflowX: "auto" }}>
              <table
                style={{
                  width: "100%",
                  fontSize: "0.875rem",
                  border: "1px solid #e4e4e7",
                }}
              >
                <thead>
                  <tr
                    style={{
                      backgroundColor: "#f8fafc",
                      borderBottom: "1px solid #e2e8f0",
                    }}
                  >
                    {currentTable?.fields
                      .filter((f) =>
                        mappings.some(
                          (m) =>
                            m.targetFieldId === f.id &&
                            m.tableId === currentTable.id
                        )
                      )
                      .map((field) => (
                        <th
                          key={field.id}
                          style={{
                            textAlign: "left",
                            padding: "0.75rem",
                            color: "#374151",
                            fontWeight: "600",
                            borderRight: "1px solid #e4e4e7",
                          }}
                        >
                          {field.name}
                          <div
                            style={{
                              fontSize: "0.75rem",
                              fontWeight: "400",
                              color: "#71717a",
                            }}
                          >
                            {field.type}
                          </div>
                        </th>
                      ))}
                  </tr>
                </thead>
                <tbody>
                  {[0, 1, 2, 3, 4].map((rowIndex) => (
                    <tr
                      key={rowIndex}
                      style={{ borderBottom: "1px solid #f1f5f9" }}
                    >
                      {currentTable?.fields
                        .filter((f) =>
                          mappings.some(
                            (m) =>
                              m.targetFieldId === f.id &&
                              m.tableId === currentTable.id
                          )
                        )
                        .map((field) => {
                          const mapping = mappings.find(
                            (m) =>
                              m.targetFieldId === field.id &&
                              m.tableId === currentTable.id
                          );
                          const sourceField = mapping
                            ? getSourceFieldById(mapping.sourceFieldId)
                            : null;
                          const sampleValue =
                            sourceField?.sampleData?.[
                              rowIndex % (sourceField.sampleData?.length || 1)
                            ] || "N/A";

                          return (
                            <td
                              key={field.id}
                              style={{
                                padding: "0.75rem",
                                color: "#1f2937",
                                borderRight: "1px solid #e4e4e7",
                              }}
                            >
                              {sampleValue}
                            </td>
                          );
                        })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div
              style={{
                display: "flex",
                gap: "1rem",
                justifyContent: "flex-end",
                marginTop: "1.5rem",
              }}
            >
              <button
                onClick={() => setShowPreview(false)}
                style={{
                  backgroundColor: "#e4e4e7",
                  color: "#3f3f46",
                  borderRadius: "0.375rem",
                  padding: "0.625rem 1.5rem",
                  fontWeight: "500",
                  fontSize: "0.875rem",
                  border: "none",
                  cursor: "pointer",
                }}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* CSS for spinner animation */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </div>
  );
}
