import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import <PERSON>anaLogo from "@/components/common/FlashanaLogo";
import * as yup from "yup";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useSignupStore } from "@/store/signupStore";
import { useToast } from "@/hooks/useToast";
import { ToastContainer } from "@/components/common/Toast";
import {
  usePostResendOtp,
  usePostVerifyOtp,
} from "@/hooks/auth/verifyOtpHooks";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";

const schema = yup.object({
  otp: yup
    .string()
    .required("OTP is required")
    .length(6, "OTP must be 6 digits"),
});

export interface VerifyOTpFormData {
  otp: string;
}

export default function OTPVerificationPage() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { toasts, removeToast, success, error } = useToast();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
  } = useForm<VerifyOTpFormData>({
    resolver: yupResolver(schema),
  });

  // Fetch user info from zustand
  const userInfo = useSignupStore.getState().userInfo;

  const onPostSuccess = (data: any) => {
    setIsLoading(false);
    success("Success", `${data?.message || "Email verified. Please login."}`);

    // Clear user info from zustand
    useSignupStore.getState().clearUserInfo();
    setTimeout(() => {
      navigate("/auth/login");
    }, 3000);
  };

  const onPostError = (errorRes: any) => {
    setIsLoading(false);
    const errorInfo =
      errorRes?.error?.message || "Something went wrong. Please try again.";
    error("Error", `${errorInfo}`);
  };

  const onResendSuccess = (data: any) => {
    success("Success", `${data?.message || "OTP resent successfully."}`);
  };

  const onResendError = (errorRes: any) => {
    const errorInfo =
      errorRes?.error?.message || "Failed to resend OTP. Please try again.";
    error("Error", `${errorInfo}`);
  };

  const { postMutate } = usePostVerifyOtp(onPostSuccess, onPostError);

  const { postMutate: resendMutate } = usePostResendOtp(
    onResendSuccess,
    onResendError
  );

  const onSubmit = async (data: VerifyOTpFormData) => {
    setIsLoading(true);
    try {
      const dataPayload: any = {
        email: userInfo?.email || "",
        user_id: userInfo?.user_id || "",
        tenant_id: userInfo?.tenant_id || "",
        confirmation_code: data.otp,
      };

      postMutate(dataPayload);
    } catch (error) {
      console.error("Signup failed:", error);
      setIsLoading(false);
    }
  };

  const onResend = async () => {
    try {
      const dataPayload: any = {
        email: userInfo?.email || "",
      };

      resendMutate(dataPayload);
    } catch (error) {
      console.error("Resend otp failed:", error);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-white">
      <div className="w-full max-w-md flex flex-col items-center px-4">
        <div className="w-full max-w-md space-y-6">
          {/* Logo and title */}
          <div className="text-center pb-[40px]">
            <div
              style={{
                marginBottom: "40px",
                display: "flex",
                justifyContent: "center",
              }}
            >
              <FlashanaLogo variant="full" size="large" animated={false} />
            </div>
            <h2 className="text-3xl text-primary text-center font-semibold mb-4">
              OTP Verification
            </h2>
            <p className="text-zinc-500 text-sm">
              Enter the OTP sent to {userInfo?.email || "your email"}
            </p>
          </div>

          {/* OTP Input */}
          <div className="w-full flex justify-center mt-6 mb-2">
            <Controller
              control={control}
              name="otp"
              rules={{
                required: "OTP is required",
                minLength: { value: 6, message: "OTP must be 6 digits" },
                maxLength: { value: 6, message: "OTP must be 6 digits" },
              }}
              render={({ field }) => (
                <InputOTP
                  maxLength={6}
                  value={field.value || ""}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  className="w-full"
                >
                  <InputOTPGroup className="w-full">
                    {[...Array(6)].map((_, i) => (
                      <InputOTPSlot key={i} index={i} />
                    ))}
                  </InputOTPGroup>
                </InputOTP>
              )}
            />
          </div>

          {errors.otp && (
            <p
              style={{
                marginTop: "0.5rem",
                fontSize: "0.75rem",
                color: "#dc2626",
              }}
            >
              {errors.otp.message}
            </p>
          )}

          {/* Submit Button */}
          <button
            onClick={handleSubmit(onSubmit)}
            disabled={isLoading}
            style={{
              width: "100%",
              backgroundColor: "#2b524f",
              color: "white",
              borderRadius: "0.375rem",
              padding: "0.625rem 1.5rem",
              fontWeight: "500",
              fontSize: "1rem",
              border: "none",
              cursor: isLoading ? "not-allowed" : "pointer",
              opacity: isLoading ? 0.5 : 1,
              transition: "all 0.2s",
            }}
          >
            {isLoading ? "Verifying OTP..." : "Submit"}
          </button>

          {/* Resend */}
          <p className="text-sm text-center text-gray-600">
            Didn’t receive the OTP?{" "}
            <span
              onClick={onResend}
              style={{
                color: "#ab732b",
                cursor: "pointer",
                fontWeight: "500",
              }}
            >
              Resend
            </span>
          </p>

          {/* Back to Signup */}
          <div className="text-center pt-6">
            <Link
              to="/auth/signup"
              className="flex items-center justify-center text-sm text-gray-700 hover:underline"
            >
              <ArrowLeft className="mr-1 w-4 h-4" />
              Back to Signup
            </Link>
          </div>
        </div>
      </div>
      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </div>
  );
}
