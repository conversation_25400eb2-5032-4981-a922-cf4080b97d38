import React, { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import FlashanaLogo from "@/components/common/FlashanaLogo";
import { usePostForgotPwd } from "@/hooks/auth/fogotPwdHooks";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useToast } from "@/hooks/useToast";
import { ToastContainer } from "@/components/common/Toast";
import { useForgotPasswordStore } from "@/store/forgotPwdStore";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const schema = yup.object({
  email: yup.string()
    .email("Invalid email")
    .required("Email is required")
    .matches(/^[^\s]+@[^\s]+\.[^\s]+$/, "Invalid email"),
});

export interface FogotPwdFormData {
  email: string;
}

const ForgotPwdPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { toasts, removeToast, success, error } = useToast();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FogotPwdFormData>({
    resolver: yupResolver(schema),
  });

  const onPostSuccess = (data: any) => {
    setIsLoading(false);
    success(
      "Success",
      `${data?.message || "Your email has been verified. Please reset your password."}`
    );
    setTimeout(() => {
      navigate("/auth/reset-password");
    }, 3000);
  };

  const onPostError = (errorRes: any) => {
    setIsLoading(false);
    const errorInfo =
      errorRes?.error?.message || "Something went wrong. Please try again.";
    error("Error", `${errorInfo}`);
  };

  const { postMutate } = usePostForgotPwd(onPostSuccess, onPostError);

  const onSubmit = async (data: FogotPwdFormData) => {
    setIsLoading(true);
    try {
      // Save email from form to zustand
      useForgotPasswordStore.getState().setEmail(data?.email || "");

      const userDataPayload: any = {
        email: data?.email || "",
      };
      postMutate(userDataPayload);
    } catch (error) {
      console.error("Forgot Password request failed:", error);
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-white">
      <div className="w-full max-w-md flex flex-col items-center px-4">
        <div className="mb-8 flex justify-center">
          <FlashanaLogo variant="full" size="large" animated={false} />
        </div>
        <h1 className="text-2xl text-primary font-semibold text-center mb-4">
          Forgot Password
        </h1>
        <p className="text-zinc-700 text-center text-sm mb-8">
          Please enter your email to send verification code.
        </p>
        <form onSubmit={handleSubmit(onSubmit)} className="w-full space-y-6">
          <div>
            <Label htmlFor="email" className="mb-1 text-sm">
              Email<span className="text-red-500">*</span>
            </Label>
            <Input
              id="email"
              type="text"
              autoComplete="email"
              {...register("email")}
              placeholder="<EMAIL>"
              className={
                errors.email
                  ? "mt-1 border-destructive focus:ring-destructive focus:border-destructive focus-visible:ring-destructive focus-visible:border-destructive"
                  : "mt-1"
              }
            />
            {errors.email && (
              <p
                className="mt-1 text-xs font-medium text-destructive"
                role="alert"
              >
                {errors.email.message}
              </p>
            )}
          </div>
          <Button
            type="submit"
            className="w-full"
            disabled={isLoading}
          >
            {isLoading ? "Submitting..." : "Submit"}
          </Button>
        </form>
        <div className="text-center mt-8 w-full">
          <Link
            to="/auth/login"
            className="flex items-center justify-center text-sm text-zinc-900 hover:underline gap-1"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to login
          </Link>
        </div>
      </div>
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </div>
  );
};

export default ForgotPwdPage;
