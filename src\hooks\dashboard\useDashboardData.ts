import { useMutation, useQuery } from '@tanstack/react-query';
import useDataService from '../../services/useDataService';
import { GET_DASHBOARD_DATA } from '../../constants/urls';

export const useGetDashboardData = (enabled = true) => {
    return useQuery<any, Error>({
        queryKey: [],
        queryFn: async () => {
            const endpoint = `${GET_DASHBOARD_DATA}`;
            return await useDataService.getService(endpoint);
        },
        enabled: enabled,
        staleTime: 0,
    });
};