/* Import Poppins font from Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme (default - matches Figma with finalized colors) */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    /* Zinc 900 for body text */
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 164 32% 19%;
    /* #2b524f - Finalized primary color */
    --primary-foreground: 0 0% 98%;
    --secondary: 34 62% 41%;
    /* #ab732b - Finalized secondary color */
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    /* Zinc 200 */
    --muted-foreground: 0 0% 45.1%;
    /* Zinc 700 for subtitles */
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    /* Zinc 200 for borders */
    --input: 240 5.9% 90%;
    --ring: #fff;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@font-face {
  font-family: "icomoon";
  src: url("./assets/fonts/icomoon.eot?1zvk9b");
  src:
    url("./assets/fonts/icomoon.eot?1zvk9b#iefix") format("embedded-opentype"),
    url("./assets/fonts/icomoon.ttf?1zvk9b") format("truetype"),
    url("./assets/fonts/icomoon.woff?1zvk9b") format("woff"),
    url("./assets/fonts/icomoon.svg?1zvk9b#icomoon") format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"],
[class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "icomoon" !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family:
      "Poppins",
      "Inter",
      -apple-system,
      BlinkMacSystemFont,
      "Segoe UI",
      "Roboto",
      "Oxygen",
      "Ubuntu",
      "Cantarell",
      "Fira Sans",
      "Droid Sans",
      "Helvetica Neue",
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Light mode first - default is light (matches Figma) */
  html {
    color-scheme: light;
  }

  /* Custom scrollbar styling */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-md;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}
.icon-Company-Information:before {
  content: "\e900";
}
.icon-Dashboard:before {
  content: "\e901";
}
.icon-Data-Ingestion:before {
  content: "\e902";
}
.icon-Forecasting:before {
  content: "\e903";
}
.icon-Prosessed-Data:before {
  content: "\e904";
}
.icon-Stores:before {
  content: "\e905";
}
.submenu-dash{
  position: relative;
}
.submenu-dash span{
  @apply px-4 py-2.5 block;
}
.submenu-dash:hover span{
  @apply bg-primary/10 rounded-md;
}
.submenu-dash::before {
    content: "";
    position: absolute;
    left: 0;
    top: 22px;
    width: 12px;
    height: 5px;
    color: #D4D4D8;
    border: 1px solid;
    border-bottom-left-radius: 5px;
    border-right-color: #fff;
    border-top-color: #fff;
}
@layer components {
  /* Flashana-styled clean components with finalized colors */
  .flashana-card {
    @apply bg-white rounded-lg shadow-sm border border-zinc-200 px-4 py-4;
  }

  .flashana-button {
    @apply bg-primary text-primary-foreground rounded-md px-6 py-2.5 font-medium transition-all duration-200 hover:bg-primary/90;
  }

  .flashana-button-secondary {
    @apply bg-secondary text-secondary-foreground rounded-md px-6 py-2.5 font-medium transition-all duration-200 hover:bg-secondary/90;
  }

  .flashana-input {
    @apply w-full px-3 py-2.5 border border-zinc-200 rounded-md bg-white text-zinc-900 placeholder:text-zinc-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent h-[38px];
  }

  .flashana-sidebar {
    @apply bg-white border-r border-zinc-200 h-full;
  }

  .flashana-nav-item {
    @apply flex items-center px-4 py-2.5 text-zinc-700 hover:bg-primary/10 transition-colors duration-200 rounded;
    font-size: 14px
    
  }

  .flashana-nav-item.active {
    @apply bg-primary text-primary-foreground;
    border-radius: 6px;
  }

  .flashana-subtitle {
    @apply text-zinc-700 font-medium;
  }

  .flashana-body-text {
    @apply text-zinc-900;
  }

  /* Status indicators */
  .status-mapped {
    @apply text-green-600 bg-green-50 border border-green-200 px-2 py-1 rounded-md text-sm font-medium;
  }

  .status-partially-mapped {
    @apply text-yellow-600 bg-yellow-50 border border-yellow-200 px-2 py-1 rounded-md text-sm font-medium;
  }

  .status-not-mapped {
    @apply text-red-600 bg-red-50 border border-red-200 px-2 py-1 rounded-md text-sm font-medium;
  }

  /* Drag and drop styles */
  .drop-zone {
    @apply border-2 border-dashed border-zinc-300 rounded-lg p-4 transition-colors duration-200;
  }

  .drop-zone.active {
    @apply border-primary bg-primary/5;
  }

  .draggable-field {
    @apply bg-white border border-zinc-200 rounded-md p-3 cursor-move hover:shadow-md transition-shadow duration-200;
  }

  .draggable-field.dragging {
    @apply opacity-50;
  }

  /* Premium shadows matching Figma */
  .figma-shadow {
    box-shadow:
      0 1px 3px 0 rgba(0, 0, 0, 0.1),
      0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .figma-shadow-lg {
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* Upload progress */
  .upload-progress {
    @apply w-full bg-zinc-200 rounded-full h-2;
  }

  .upload-progress-bar {
    @apply h-2 bg-primary rounded-full transition-all duration-300;
  }

  /* Accordion styles */
  .accordion-header {
    @apply flex items-center justify-between w-full p-4 text-left bg-zinc-50 border border-zinc-200 rounded-lg hover:bg-zinc-100 transition-colors duration-200;
  }

  .accordion-content {
    @apply border-l border-r border-b border-zinc-200 rounded-b-lg p-4 bg-white;
  }
}

.custom-multi-select button > div{
  @apply overflow-hidden whitespace-nowrap text-ellipsis max-w-full;
}

@media screen and (max-height: 650px) {
 .wrapper >div{
  justify-content: flex-start !important;
 }
 .terms{
  @apply py-6;
 }
}
