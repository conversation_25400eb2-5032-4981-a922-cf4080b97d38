import { useEffect, useRef, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import InteractiveForecastChart from "@/components/forecasting/InteractiveForecastChart";
import {
  historicalRevenueData,
  forecastRevenueData,
} from "@/data/forecastMockData";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useGetForecastResults } from "@/hooks/generateForcast/generateForcastHooks";
import { useToast } from "@/hooks/useToast";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { useLocation } from "react-router-dom";

interface ForecastData {
  date: string;
  productSKU: string;
  store: string;
  actual?: number;
  forecast: number;
  lowerBound: number;
  upperBound: number;
}

// Mock data that includes actual past sales data
const mockPastForecastTableData: ForecastData[] = [
  {
    date: "12-05-24",
    productSKU: "KPF007",
    store: "Toronto",
    actual: 475,
    forecast: 480,
    lowerBound: 455,
    upperBound: 510,
  },
  {
    date: "08-02-25",
    productSKU: "VQ0001",
    store: "Toronto",
    actual: 488,
    forecast: 495,
    lowerBound: 470,
    upperBound: 530,
  },
  {
    date: "11-25-25",
    productSKU: "WH8002",
    store: "Toronto",
    forecast: 510,
    lowerBound: 475,
    upperBound: 545,
  },
];

interface ChartDataPoint {
  date: string;
  actual?: number;
  value: number;
  confidence_lower?: number;
  confidence_upper?: number;
}

const mockHistoricalData: ChartDataPoint[] = [];

export default function PastForecastPage() {
  const { forecastId } = useParams();
  const location = useLocation();
  const forecastData = location.state?.forecast;
  const navigate = useNavigate();
  const [forecastResultData, setForecastResultData] = useState([]);
  const [historicalData, setHistoricalData] = useState([]);
  const [actualsData, setActualsData] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const { toasts, removeToast, success, error } = useToast();

  console.log("forecastResultData", forecastResultData);
  console.log("historicalData", historicalData);
  console.log("actualsData", actualsData);

  const formattedHistoricalData = mockHistoricalData.map((d) => ({
    date: d.date,
    value: d.value,
  }));

  console.log("Vurrent data", forecastResultData);
  // Format forecast data
  const formattedForecastData = forecastResultData.map((d) => ({
    date: d.forecast_date,
    value: d.forecast_quantity,
    confidence_lower: d.forecast_lower,
    confidence_upper: d.forecast_upper,
  }));

  const { checkResult } = useGetForecastResults(
    (data) => {
      console.log(data);
      const resultArrayForecast = data.result.forecast_data || [];
      const resultArrayHistorical = data.result.historical_data || [];
      const resultArrayActuals = data.result.actuals_data || [];
      setForecastResultData(resultArrayForecast);
      setHistoricalData(resultArrayHistorical);
      setActualsData(resultArrayActuals);
    },
    (errorInfo) => {
      error("Error", `${errorInfo}`);
    }
  );

  const hasFetched = useRef(false);

  useEffect(() => {
    if (forecastId && !hasFetched.current) {
      hasFetched.current = true;
      checkResult(forecastId);
    }
  }, [forecastId]);
  // Mock forecast settings based on forecastId
  const forecastSettings = {
    batch: "Jan_Batch_2025",
    algorithm: "Prophet",
    sku: "abc123",
    horizon: "30",
    filterBy: "SKU",
    label: "Store A-Q3-Forecast",
    includeFactors: "Weather",
  };

  const handleBack = () => {
    navigate("/forecasting/list");
  };

  // // Combine historical and forecast data for the chart
  // const combinedHistoricalData = historicalRevenueData.map((item) => ({
  //   ...item,
  //   actual: item.actual || item.predicted,
  // }));

  // const combinedForecastData = forecastRevenueData.map((item, index) => {
  //   // Mock some actual data for past dates
  //   const isPastDate = index < 10; // First 10 items are past dates with actual data
  //   return {
  //     ...item,
  //     actual: isPastDate
  //       ? item.predicted + (Math.random() - 0.5) * 20
  //       : undefined,
  //   };
  // });

  return (
    <>
      <div className="mb-2 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={handleBack}
            className="p-2 hover:bg-muted rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <h1 className="font-medium">Back</h1>
        </div>
      </div>

      {/* Forecast Settings Display */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <Label
                htmlFor="label"
                className="mb-2 block text-zinc-600 font-normal"
              >
                Forecast Label
              </Label>
              <Input
                id="label"
                value={forecastData.name}
                readOnly
                className="text-md font-medium p-0 border-hidden focus:outline-none focus:ring-0"
              />
            </div>
            {/* <div>
              <Label
                htmlFor="batch"
                className="mb-2 block text-zinc-600 font-normal"
              >
                Selected Batch
              </Label>
              <Input
                id="batch"
                value={forecastSettings.batch}
                readOnly
                className="text-md font-medium p-0 border-hidden"
              />
            </div> */}
            <div>
              <Label
                htmlFor="algorithm"
                className="mb-2 block text-zinc-600 font-normal"
              >
                Select Algorithm
              </Label>
              <Input
                id="algorithm"
                value={forecastData.model}
                readOnly
                className="text-md font-medium p-0 border-hidden focus:outline-none focus:ring-0"
              />
            </div>
            <div>
              <Label
                htmlFor="sku"
                className="mb-2 block text-zinc-600 font-normal"
              >
                Select SKU
              </Label>
              <Input
                id="sku"
                value={
                  forecastData.sku_codes.length > 0
                    ? forecastData.sku_codes.join(", ")
                    : "-"
                }
                readOnly
                className="text-md font-medium p-0 border-hidden focus:outline-none focus:ring-0"
              />
            </div>
            <div>
              <Label
                htmlFor="horizon"
                className="mb-2 block text-zinc-600 font-normal"
              >
                Forecast Horizon (in days)
              </Label>
              <Input
                id="horizon"
                value={forecastData.forecast_days}
                readOnly
                className="text-md font-medium p-0 border-hidden focus:outline-none focus:ring-0"
              />
            </div>
            {/* <div>
              <Label
                htmlFor="filterBy"
                className="mb-2 block text-zinc-600 font-normal"
              >
                Filter by Store / Product / SKU
              </Label>
              <Input
                id="filterBy"
                value={forecastSettings.filterBy}
                readOnly
                className="text-md font-medium p-0 border-hidden"
              />
            </div> */}
            <div>
              <Label
                htmlFor="factors"
                className="mb-2 block text-zinc-600 font-normal"
              >
                Include Factors
              </Label>
              <Input
                id="factors"
                value={
                  forecastData.factors.length > 0
                    ? forecastData.factors.join(", ")
                    : "-"
                }
                readOnly
                className="text-md font-medium p-0 border-hidden focus:outline-none focus:ring-0"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Chart Section */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Forecast Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          <InteractiveForecastChart
            historicalData={historicalData}
            forecastData={formattedForecastData}
            actualsData={[]}
            isLoading={isGenerating}
          />

          {/* <InteractiveForecastChart
            historicalData={[
              {
                "date": "2022-01-01",
                "value": 22126
              },
              {
                "date": "2022-01-08",
                "value": 24887
              },
              {
                "date": "2022-01-15",
                "value": 26215
              },
              {
                "date": "2022-01-22",
                "value": 26543
              },
              {
                "date": "2022-01-29",
                "value": 27151
              },
              {
                "date": "2022-02-05",
                "value": 28413
              },
              {
                "date": "2022-02-12",
                "value": 28954
              },
              {
                "date": "2022-02-19",
                "value": 29607
              },
              {
                "date": "2022-02-26",
                "value": 27411
              },
              {
                "date": "2022-03-05",
                "value": 27130
              },
              {
                "date": "2022-03-12",
                "value": 26612
              },
              {
                "date": "2022-03-19",
                "value": 26761
              },
              {
                "date": "2022-03-26",
                "value": 25675
              },
              {
                "date": "2022-04-02",
                "value": 24263
              },
              {
                "date": "2022-04-09",
                "value": 22142
              },
              {
                "date": "2022-04-16",
                "value": 22676
              },
              {
                "date": "2022-04-23",
                "value": 22860
              },
              {
                "date": "2022-04-30",
                "value": 23177
              },
              {
                "date": "2022-05-07",
                "value": 24358
              },
              {
                "date": "2022-05-14",
                "value": 25424
              }
            ]}
            forecastData={
              [
                {
                  "date": "2022-06-25",
                  "value": 30067,
                  "confidence_lower": 27452,
                  "confidence_upper": 32682
                },
                {
                  "date": "2022-07-02",
                  "value": 27709,
                  "confidence_lower": 25264,
                  "confidence_upper": 30154
                },
                {
                  "date": "2022-07-09",
                  "value": 26657,
                  "confidence_lower": 24270,
                  "confidence_upper": 29044
                },
                {
                  "date": "2022-07-16",
                  "value": 25190,
                  "confidence_lower": 22900,
                  "confidence_upper": 27480
                },
                {
                  "date": "2022-07-23",
 
                  "value": 24768,
 
                  "confidence_lower": 22482,
 
                  "confidence_upper": 27054
 
                },
 
                {
 
                  "date": "2022-07-30",
 
                  "value": 26631,
 
                  "confidence_lower": 24134,
 
                  "confidence_upper": 29128
 
                },
 
                {
 
                  "date": "2022-08-06",
 
                  "value": 24111,
 
                  "confidence_lower": 21815,
 
                  "confidence_upper": 26407
 
                },
 
                {
 
                  "date": "2022-08-13",
 
                  "value": 27223,
 
                  "confidence_lower": 24589,
 
                  "confidence_upper": 29857
 
                },
 
                {
 
                  "date": "2022-08-20",
 
                  "value": 30635,
 
                  "confidence_lower": 27622,
 
                  "confidence_upper": 33648
 
                },
 
                {
 
                  "date": "2022-08-27",
 
                  "value": 27683,
 
                  "confidence_lower": 24915,
 
                  "confidence_upper": 30451
 
                }
 
              ]
            }
            // actualsData={[]}
            actualsData={[
              {
                "date": "2022-05-21",
                "value": 28847
              },
              {
                "date": "2022-05-28",
                "value": 32046
              },
              {
                "date": "2022-06-04",
                "value": 29293
              },
              {
                "date": "2022-06-11",
                "value": 28552
              },
              {
                "date": "2022-06-18",
                "value": 29556
              }
            ]}
            isLoading={isGenerating}
          /> */}
        </CardContent>
      </Card>

      {/* Forecast Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Forecast Table
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table className="border">
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Product/SKU</TableHead>
                  <TableHead>Store</TableHead>
                  <TableHead className="text-center">Actual Sales</TableHead>
                  <TableHead className="text-right">Forecast (y)</TableHead>
                  <TableHead className="text-right">Lower Bound (y-lower)</TableHead>
                  <TableHead className="text-right">Upper Bound (y-upper)</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {forecastResultData.map((row, index) => (
                  <TableRow key={index} className="hover:bg-muted/50">
                    <TableCell>{row.forecast_date}</TableCell>
                    <TableCell>{row.sku_code}</TableCell>
                    <TableCell>{row.location_name}</TableCell>
                    <TableCell className="text-center">
                      {row.actual ?? "-"}
                    </TableCell>
                    <TableCell className="text-right">
                      {row.forecast_quantity}
                    </TableCell>
                    <TableCell className="text-right">
                      {row.forecast_lower}
                    </TableCell>
                    <TableCell className="text-right">
                      {row.forecast_upper}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
