import { useQuery } from "@tanstack/react-query";
import useDataService from "@/services/useDataService";
import { GET_STORELIST_URL } from "@/constants/urls";

export interface Store {
  location_id: number;
  location_name: string;
  location_type_id: number;
  contact_name: string | null;
  contact_email: string | null;
  tenant_id: string;
  created_at: string;
}

interface GetStoreListResponse {
  success: boolean;
  message: string;
  code: number;
  result: {
    data: Store[];
    total_items: number;
    total_pages: number;
    current_page: number;
    page_size: number;
  };
}

export const useGetStoreList = (onSuccessData?: any, storeParams?: { tenentId?: any; size?: any; sort_column?: any; sort_order?: any; }, apiCall?: any, onErrorData?: (error: any) => void, currentPage?: any, sortOrder?: any) => {
  return useQuery<GetStoreListResponse>({
    queryKey: ["store-list", sortOrder],
    queryFn: async () => {
      const endpoint = `${GET_STORELIST_URL}?sort_order=${sortOrder}`;
      return await useDataService.getService(endpoint);
    },
    staleTime: 0,              // ✅ Cache forever
  });
};