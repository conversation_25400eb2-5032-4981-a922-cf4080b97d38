import { format, parse, isValid } from "date-fns";

/**
 * Formats date strings into MM-dd-yyyy.
 * If invalid, removes time and returns the date part.
 */
export const formatDateValue = (value: any): string => {
  if (typeof value === "string") {
    const trimmed = value.trim();

    // Ignore numeric-only strings
    if (/^\d+$/.test(trimmed)) return trimmed;

    // Try parsing full datetime format
    const parsed = parse(trimmed, "d/M/yyyy hh:mm:ss a", new Date());

    if (isValid(parsed)) {
      return format(parsed, "MM-dd-yyyy");
    }

    // If invalid, but contains both date and time (e.g., 20/20/2025 10:56:00 PM)
    const parts = trimmed.split(" ");
    const maybeDatePart = parts[0];

    // If date part exists, return it only (e.g., "20/20/2025")
    if (maybeDatePart) return maybeDatePart;
  }

  return String(value);
};
