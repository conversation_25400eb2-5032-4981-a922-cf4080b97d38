import { create } from 'zustand';

interface SignupInfo {
  email: string;
  user_id: string;
  tenant_id: string;
}

interface SignupState {
  userInfo: SignupInfo | null;
  setUserInfo: (info: SignupInfo) => void;
  clearUserInfo: () => void;
}

export const useSignupStore = create<SignupState>((set) => ({
  userInfo: JSON.parse(localStorage.getItem('flashana-signup-user') || 'null'),

  setUserInfo: (info) => {
    localStorage.setItem('flashana-signup-user', JSON.stringify(info));
    set({ userInfo: info });
  },

  clearUserInfo: () => {
    localStorage.removeItem('flashana-signup-user');
    set({ userInfo: null });
  },
}));