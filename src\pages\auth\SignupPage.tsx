import { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Eye, EyeOff } from "lucide-react";
import <PERSON><PERSON><PERSON><PERSON> from "@/components/common/FlashanaLogo";
import { usePostSignup } from "../../hooks/auth/signupHooks";
import { useSignupStore } from "@/store/signupStore";
import { useToast } from "@/hooks/useToast";
import { ToastContainer } from "@/components/common/Toast";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import loginImg from "../../assets/login-building.png";
import { useGetCompanyTypes } from "../../hooks/CompanyDetails/useCompanyDetails";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";

const schema = yup.object({
  firstName: yup.string().required("First Name is required"),
  lastName: yup.string().required("Last Name is required"),
  phoneNumber: yup.string().required("Phone Number is required"),
  email: yup.string().email("Invalid email").required("Email is required"),
  companyName: yup.string().required("Company Name is required"),
  companyType: yup.string().required("Company Type is required"),
  password: yup
    .string()
    .required("Password is required")
    .min(6, "Password must be at least 6 characters")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z0-9])(?!.*\s).{8,}$/,
      "Invalid password (must include uppercase, lowercase, number, special character)"
    ),
  confirmPassword: yup
    .string()
    .required("Confirm Password is required")
    .oneOf([yup.ref("password")], "Passwords must match"),
  agreeToTerms: yup
    .boolean()
    .oneOf([true], "You must agree to Terms of Service and Privacy Policy"),
});

export interface SignupFormData {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  companyName: string;
  companyType: string;
  password: string;
  confirmPassword: string;
  agreeToTerms: boolean;
}

export default function SignupPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toasts, removeToast, success, error } = useToast();
  const navigate = useNavigate();
  const { data: companyTypes, isLoading: isCompanyTypesLoading } =
    useGetCompanyTypes();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    control,
    formState: { errors },
  } = useForm<SignupFormData>({
    resolver: yupResolver(schema),
  });

  const onPostSuccess = (data: any) => {
    setIsLoading(false);
    success("Success", `${data?.message || "Signed up successful."}`);
    const store = useSignupStore.getState();
    const currentEmail = store.userInfo?.email || "";
    const updatedInfo = {
      email: currentEmail,
      user_id: data?.result?.data?.user_id || "",
      tenant_id: data?.result?.data?.tenant_id || "",
    };
    store.setUserInfo(updatedInfo);
    setTimeout(() => {
      navigate("/auth/verify-otp");
    }, 3000);
  };

  const onPostError = (errorRes: any) => {
    setIsLoading(false);
    const errorInfo =
      errorRes?.error?.message || "Something went wrong. Please try again.";
    error("Error", `${errorInfo}`);
  };

  const { postMutate } = usePostSignup(onPostSuccess, onPostError);

  const onSubmit = async (data: SignupFormData) => {
    setIsLoading(true);
    try {
      useSignupStore
        .getState()
        .setUserInfo({ email: data.email, user_id: "", tenant_id: "" });

      // Extract country code and phone number
      const phoneValue = data.phoneNumber || "";
      const phone_country_code = phoneValue.startsWith("1") ? "1" : ""; // Canada code
      const phone_number = phone_country_code
        ? phoneValue.slice(1)
        : phoneValue;

      const userDataPayload: any = {
        email: data.email,
        password: data.password,
        first_name: data.firstName,
        last_name: data.lastName,
        phone_country_code,
        phone_number,
        country: "Canada",
        tenant_name: data.companyName,
        industry_id: Number(data.companyType),
      };

      postMutate(userDataPayload);
    } catch (error) {
      console.error("Signup failed:", error);
      setIsLoading(false);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 h-screen">
      {/* Left side - Building image */}
      <div className="w-full h-full overflow-hidden">
        <img
          src={loginImg}
          alt="Login"
          className="w-full h-full object-cover"
        />
      </div>
      {/* Right side - Signup form */}
      <div className="wrapper flex justify-center bg-white p-[40px] h-screen min-h-0 overflow-y-auto">
        <div className="w-full max-w-md flex flex-col xxl:justify-center">
          <div className="flex flex-col justify-center flex-1 py-6">
            {/* Logo and title */}
            <div className="text-center pb-[32px]">
              <div
                style={{
                  marginBottom: "32px",
                  display: "flex",
                  justifyContent: "center",
                }}
              >
                <FlashanaLogo variant="full" size="large" animated={false} />
              </div>
              <h2 className="text-3xl text-primary text-center font-semibold mb-4">
                Signup
              </h2>
              <p className="text-zinc-500">
                Create your account to get started
              </p>
            </div>
            {/* Signup form */}
            <form
              onSubmit={handleSubmit(onSubmit)}
              className="flex flex-col gap-4"
            >
              {/* Name fields */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">
                    First Name<span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="firstName"
                    {...register("firstName")}
                    placeholder="John"
                    className={errors.firstName ? "border-destructive" : ""}
                    onInput={e => {
                      const target = e.target as HTMLInputElement;
                      target.value = target.value.replace(/\s/g, "");
                    }}
                  />
                  {errors.firstName && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.firstName.message}
                    </p>
                  )}
                </div>
                <div>
                  <Label htmlFor="lastName">
                    Last Name<span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="lastName"
                    {...register("lastName")}
                    placeholder="Doe"
                    className={errors.lastName ? "border-destructive" : ""}
                    onInput={e => {
                      const target = e.target as HTMLInputElement;
                      target.value = target.value.replace(/\s/g, "");
                    }}
                  />
                  {errors.lastName && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.lastName.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Phone Number */}
              <div>
                <Label htmlFor="phoneNumber">
                  Phone Number<span className="text-red-500">*</span>
                </Label>
                <Controller
                  name="phoneNumber"
                  control={control}
                  render={({ field }) => (
                    <PhoneInput
                      country="ca"
                      onlyCountries={["ca"]}
                      disableDropdown={true}
                      value={field.value}
                      inputProps={{
                        className: `w-full border-zinc-300 rounded pl-[50px] focus-visible:ring-primary focus-visible:border-primary `,
                      }}

                      onChange={(value) => {
                        // Always start with '1' for Canada
                        if (!value.startsWith("1")) {
                          field.onChange("1" + value.replace(/^1/, ""));
                        } else {
                          field.onChange(value);
                        }
                      }}
                      placeholder="************"
                    />
                  )}
                />
                {errors.phoneNumber && (
                  <p className="mt-1 text-xs text-destructive">
                    {errors.phoneNumber.message}
                  </p>
                )}
              </div>
              {/* Email */}
              <div>
                <Label htmlFor="email">
                  Email<span className="text-red-500">*</span>
                </Label>
                <Input
                  id="email"
                  type="text"
                  {...register("email")}
                  placeholder="<EMAIL>"
                  className={errors.email ? "border-destructive" : ""}
                  onInput={e => {
                    const target = e.target as HTMLInputElement;
                    target.value = target.value.replace(/\s/g, "");
                  }}
                />
                {errors.email && (
                  <p className="mt-1 text-xs text-destructive">
                    {errors.email.message}
                  </p>
                )}
              </div>

              {/* Company Name */}
              <div>
                <Label htmlFor="companyName">
                  Company Name<span className="text-red-500">*</span>
                </Label>
                <Input
                  id="companyName"
                  {...register("companyName")}
                  placeholder="Your Company Inc."
                  className={errors.companyName ? "border-destructive" : ""}
                  onInput={e => {
                    const target = e.target as HTMLInputElement;
                    target.value = target.value.replace(/\s/g, "");
                  }}
                />
                {errors.companyName && (
                  <p className="mt-1 text-xs text-destructive">
                    {errors.companyName.message}
                  </p>
                )}
              </div>

              {/* Company Type */}
              <div>
                <Label htmlFor="companyType">
                  Company Type<span className="text-red-500">*</span>
                </Label>
                <Select
                  value={watch("companyType")}
                  onValueChange={(val) =>
                    setValue("companyType", val, { shouldValidate: true })
                  }
                >
                  <SelectTrigger
                    id="companyType"
                    className={errors.companyType ? "border-destructive" : ""}
                  >
                    <SelectValue
                      placeholder={
                        isCompanyTypesLoading ? "Loading..." : "Select"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {isCompanyTypesLoading ? (
                      <SelectItem value="loading" disabled>
                        Loading...
                      </SelectItem>
                    ) : (
                      companyTypes?.map((type) => (
                        <SelectItem
                          key={type.industry_id}
                          value={type.industry_id.toString()}
                        >
                          {type.industry_name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {errors.companyType && (
                  <p className="mt-1 text-xs text-destructive">
                    {errors.companyType.message}
                  </p>
                )}
              </div>

              {/* Password fields */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="password">
                    Password<span className="text-red-500">*</span>
                  </Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      {...register("password")}
                      placeholder="••••••••"
                      className={
                        errors.password ? "border-destructive pr-8" : "pr-8"
                      }
                      onInput={e => {
                        const target = e.target as HTMLInputElement;
                        target.value = target.value.replace(/\s/g, "");
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-2 top-1/2 -translate-y-1/2 text-zinc-500"
                      tabIndex={-1}
                    >
                      {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>
                  {errors.password && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.password.message}
                    </p>
                  )}
                </div>
                <div>
                  <Label htmlFor="confirmPassword">
                    Confirm Password<span className="text-red-500">*</span>
                  </Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      {...register("confirmPassword")}
                      placeholder="••••••••"
                      className={
                        errors.confirmPassword
                          ? "border-destructive pr-8"
                          : "pr-8"
                      }
                      onInput={e => {
                        const target = e.target as HTMLInputElement;
                        target.value = target.value.replace(/\s/g, "");
                      }}
                    />
                    <button
                      type="button"
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                      className="absolute right-2 top-1/2 -translate-y-1/2 text-zinc-500"
                      tabIndex={-1}
                    >
                      {showConfirmPassword ? (
                        <EyeOff size={16} />
                      ) : (
                        <Eye size={16} />
                      )}
                    </button>
                  </div>
                  {errors.confirmPassword && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.confirmPassword.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Terms agreement */}
              <div>
                <div className="flex items-start gap-2 pb-4">
                  <Controller
                    name="agreeToTerms"
                    control={control}
                    defaultValue={false}
                    render={({ field }) => (
                      <Checkbox
                        id="agreeToTerms"
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    )}
                  />
                  <Label
                    htmlFor="agreeToTerms"
                    className="text-xs text-zinc-700 font-normal"
                  >
                    By signing up you agree to our{" "}
                    <Link
                      to="/terms"
                      className="text-secondary font-medium hover:underline"
                    >
                      Terms of Service
                    </Link>{" "}
                    and{" "}
                    <Link
                      to="/privacy"
                      className="text-secondary font-medium hover:underline"
                    >
                      Privacy Policy
                    </Link>
                  </Label>
                </div>
                {errors.agreeToTerms && (
                  <p className="mt-1 text-xs text-destructive">
                    {errors.agreeToTerms.message}
                  </p>
                )}
              </div>

              {/* Signup button */}
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <span className="flex items-center justify-center">
                    <span className="animate-spin mr-2 h-5 w-5 border-2 border-white border-t-transparent rounded-full" />
                    Creating Account...
                  </span>
                ) : (
                  "Sign Up"
                )}
              </Button>
            </form>
            {/* Login link */}
            <div className="text-center py-6">
              <p className="text-sm text-zinc-500">
                Already have an account?{" "}
                <Link
                  to="/auth/login"
                  className="text-secondary font-medium hover:underline"
                >
                  Log In
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </div>
  );
}
