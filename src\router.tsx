import { createBrowserRouter, Navigate } from "react-router-dom";
import MainLayout from "./components/layout/MainLayout";
import { useAuthStore } from "./store/authStore";

// Auth Pages
import LoginPage from "./pages/auth/LoginPage";
import SignupPage from "./pages/auth/SignupPage";
import VerifyOtpPage from "./pages/auth/VerifyOtpPage";
import ForgotPwdPage from "./pages/auth/FogotPwdPage";
import ResetPwdPage from "./pages/auth/ResetPwdPage";

// Dashboard
import DashboardPage from "./pages/dashboard/DashboardPage";

// Company Pages
import CompanyDetailsPage from "./pages/company/CompanyDetailsPage";

// Store Pages
import StoreListPage from "./pages/stores/StoreListPage";

// Data Ingestion Pages
import BatchListPage from "./pages/data-ingestion/BatchListPage";
import FieldMappingPage from "./pages/data-ingestion/FieldMappingPage";
import ValidationPage from "./pages/data-ingestion/ValidationPage";
import ImportedDataPage from "./pages/data-ingestion/ImportedDataPage";

// Forecasting Pages
import ForecastListPage from "./pages/forecasting/ForecastListPage";
import GenerateForecastPage from "./pages/forecasting/GenerateForecastPage";
import PastForecastPage from "./pages/forecasting/PastForecastPage";

// Settings Pages
import HolidayListPage from "./pages/company/HolidayListPage";

// Profile Pages
// import ProfilePage from "./pages/profile/ProfilePage";

// Protected Route Component
function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { isAuthenticated } = useAuthStore();

  if (!isAuthenticated) {
    return <Navigate to="/auth/login" replace />;
  }

  return <>{children}</>;
}

// Placeholder component for coming soon pages
function ComingSoonPage({ title }: { title: string }) {
  return (
    <div className="p-6">
      <div className="flashana-card text-center py-12">
        <h2 className="text-2xl font-semibold flashana-body-text mb-4">
          {title}
        </h2>
        <p className="flashana-subtitle">This page is coming soon...</p>
        <div className="mt-4 text-sm text-zinc-500">
          <p>Demo users available for testing:</p>
          <div className="mt-2 space-y-1">
            <div>
              <strong>Admin:</strong> <EMAIL> / admin123
            </div>
            <div>
              <strong>Manager:</strong> <EMAIL> / manager123
            </div>
            <div>
              <strong>Analyst:</strong> <EMAIL> / analyst123
            </div>
            <div>
              <strong>User:</strong> <EMAIL> / user123
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export const router = createBrowserRouter([
  // Redirect root to auth
  {
    path: "/",
    element: <Navigate to="/auth/login" replace />,
  },

  // Auth Routes
  {
    path: "/auth/login",
    element: <LoginPage />,
  },
  {
    path: "/auth/signup",
    element: <SignupPage />,
  },
  {
    path: "/auth/verify-otp",
    element: <VerifyOtpPage />,
  },
  {
    path: "/auth/forgot-password",
    element: <ForgotPwdPage />,
  },
  {
    path: "/auth/reset-password",
    element: <ResetPwdPage />,
  },

  // Protected Routes
  {
    path: "/",
    element: (
      <ProtectedRoute>
        <MainLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        path: "dashboard",
        element: <DashboardPage />,
      },
      {
        path: "stores",
        element: <StoreListPage />,
      },
      {
        path: "company",
        element: <Navigate to="/company/details" replace />,
      },
      {
        path: "company/details",
        element: <CompanyDetailsPage />,
      },
      {
        path: "company/holidays",
        element: <HolidayListPage />,
      },
      {
        path: "data-ingestion",
        element: <Navigate to="/data-ingestion/batches" replace />,
      },
      {
        path: "data-ingestion/batches",
        element: <BatchListPage />,
      },
      {
        path: "data-ingestion/imported",
        element: <ImportedDataPage />,
      },
      {
        path: "data-ingestion/mapping/:batchId",
        element: <FieldMappingPage />,
      },
      {
        path: "data-ingestion/validation/:batchId",
        element: <ValidationPage handleStepper={() => {}} />,
      },
      {
        path: "forecasting/list",
        element: <ForecastListPage />,
      },
      {
        path: "forecasting/generate",
        element: <GenerateForecastPage />,
      },
      {
        path: "forecasting/past/:forecastId",
        element: <PastForecastPage />,
      },
      // {
      //   path: "profile",
      //   element: <ProfilePage />,
      // },
    ],
  },

  // Catch all route
  {
    path: "*",
    element: (
      <div className="min-h-screen flex items-center justify-center bg-zinc-50">
        <div className="text-center">
          <h1 className="text-4xl font-bold flashana-body-text mb-4">404</h1>
          <p className="flashana-subtitle mb-4">Page not found</p>
          <a href="/auth/login" className="flashana-button">
            Go to Login
          </a>
        </div>
      </div>
    ),
  },
]);
